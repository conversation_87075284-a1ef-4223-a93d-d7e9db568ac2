"""
视频处理相关的Celery任务
"""

from celery import current_task, chain, group
from app.tasks.celery import celery
from app.core.database import SessionLocal
from app.models.task import Video, AnalysisResult, Task
from app.services.task_service import TaskService
from app.services.video_analysis_service import VideoAnalysisService
from app.services.subtitle_service import SubtitleService
from app.services.ffmpeg_service import ffmpeg_service
from app.services.scene_detection_service import SceneDetectionService
import os
import time
import json
from loguru import logger


def safe_update_task_state(task_instance, state, meta=None):
    """安全地更新Celery任务状态，处理非Celery环境"""
    try:
        if hasattr(task_instance, 'request') and task_instance.request.id:
            if state == 'PROGRESS':
                current_task.update_state(state=state, meta=meta)
            else:
                task_instance.update_state(state=state, meta=meta)
    except Exception:
        pass  # 忽略非Celery环境的错误


@celery.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 60})
def analyze_video_basic_info(self, video_id: int):
    """分析视频基础信息"""
    db = SessionLocal()
    try:
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise Exception(f"Video {video_id} not found")

        # 检查依赖
        deps = ffmpeg_service.check_dependencies()
        if not deps["ffprobe"]:
            raise Exception("ffprobe not available")

        # 更新任务状态
        task_service = TaskService(db)
        task_service.set_status(video.task_id, "processing")

        # 更新进度
        safe_update_task_state(self, 'PROGRESS',
            {'current': 1, 'total': 4, 'status': '分析视频元数据...'})

        if video.task_id:
            task_service.update_progress(video.task_id, 25)

        # 使用真实的视频分析服务
        analysis_service = VideoAnalysisService(db)
        metadata = analysis_service.analyze_video_metadata(video_id)

        # 更新进度
        safe_update_task_state(self, 'PROGRESS',
            {'current': 2, 'total': 4, 'status': '提取音频轨道...'})
        if video.task_id:
            task_service.update_progress(video.task_id, 50)

        # 提取音频轨道
        audio_output_dir = f"uploads/audio/video_{video_id}"
        os.makedirs(audio_output_dir, exist_ok=True)
        extracted_audio_files = analysis_service.extract_audio_tracks(video_id, audio_output_dir)

        # 更新进度
        safe_update_task_state(self, 'PROGRESS',
            {'current': 3, 'total': 4, 'status': '提取视频帧...'})
        if video.task_id:
            task_service.update_progress(video.task_id, 75)

        # 提取视频帧
        frame_output_dir = "uploads/frames"
        os.makedirs(frame_output_dir, exist_ok=True)
        frame_files = analysis_service.extract_video_frames(video_id, frame_output_dir, fps=1.0)

        # 更新进度 - 场景检测
        safe_update_task_state(self, 'PROGRESS',
            {'current': 4, 'total': 6, 'status': '检测场景切换...'})
        if video.task_id:
            task_service.update_progress(video.task_id, 85)

        # 场景检测
        scene_service = SceneDetectionService(db)
        scenes = scene_service.detect_scenes(
            video_id=video_id,
            detector_type="content",
            threshold=30.0,
            min_scene_len=1.0
        )

        # 更新进度 - 比特率分析
        safe_update_task_state(self, 'PROGRESS',
            {'current': 5, 'total': 6, 'status': '分析比特率统计...'})
        if video.task_id:
            task_service.update_progress(video.task_id, 90)

        # 比特率分析
        bitrate_stats = analysis_service.analyze_video_bitrate_stats(
            video_id=video_id,
            stream_type="video",
            aggregation="time",
            chunk_size=30.0
        )

        # 更新进度 - 生成综合信息
        safe_update_task_state(self, 'PROGRESS',
            {'current': 6, 'total': 6, 'status': '生成综合信息...'})
        if video.task_id:
            task_service.update_progress(video.task_id, 95)

        # 生成综合信息JSON
        comprehensive_json_path = analysis_service.generate_comprehensive_video_info(video_id)

        # 保存分析结果
        result_data = {
            "metadata": metadata,
            "audio_tracks_extracted": len(extracted_audio_files),
            "frames_extracted": len(frame_files),
            "audio_files": extracted_audio_files,
            "frame_files": frame_files[:10],  # 只保存前10个帧文件路径
            "scene_detection": {
                "total_scenes": len(scenes),
                "scenes": scenes[:10],  # 只返回前10个场景
                "avg_scene_duration": sum(s["duration"] for s in scenes) / len(scenes) if scenes else 0
            },
            "bitrate_stats": {
                "avg_fps": bitrate_stats.avg_fps,
                "num_frames": bitrate_stats.num_frames,
                "avg_bitrate": bitrate_stats.avg_bitrate,
                "max_bitrate": bitrate_stats.max_bitrate,
                "min_bitrate": bitrate_stats.min_bitrate,
                "duration": bitrate_stats.duration,
                "chunks": len(bitrate_stats.bitrate_per_chunk) if bitrate_stats.bitrate_per_chunk else 0
            },
            "comprehensive_json_path": comprehensive_json_path
        }

        analysis_result = AnalysisResult(
            video_id=video_id,
            step="basic_info",
            result=result_data,
            confidence=0.95,
            processing_time=time.time()
        )

        db.add(analysis_result)

        # 更新视频状态
        video.status = "analyzed"

        # 完成任务
        safe_update_task_state(self, 'PROGRESS',
            {'current': 4, 'total': 4, 'status': '分析完成'})
        if video.task_id:
            task_service.update_progress(video.task_id, 100)
            task_service.set_status(video.task_id, "completed")

        db.commit()

        return {
            'status': 'completed',
            'result': result_data,
            'video_id': video_id
        }

    except Exception as e:
        # 错误处理
        error_message = str(e)

        # 更新视频状态为失败
        video.status = "failed"

        # 更新任务状态
        if video.task_id:
            task_service = TaskService(db)
            task_service.set_status(video.task_id, "failed")
            task_service.update_task(video.task_id, error_message=error_message)

        db.commit()

        # 更新Celery任务状态
        safe_update_task_state(self, 'FAILURE', {
            'error': error_message,
            'video_id': video_id,
            'retry_count': getattr(self.request, 'retries', 0) if hasattr(self, 'request') else 0,
            'max_retries': getattr(self, 'max_retries', 3)
        })
        raise e
    finally:
        db.close()


@celery.task(bind=True)
def generate_video_subtitles(self, video_id: int, audio_track_id: int = None):
    """生成视频字幕"""
    db = SessionLocal()
    try:
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise Exception(f"Video {video_id} not found")

        # 更新任务状态
        task_service = TaskService(db)
        task_service.set_status(video.task_id, "processing")

        current_task.update_state(
            state='PROGRESS',
            meta={'current': 1, 'total': 2, 'status': '生成自动字幕...'}
        )
        task_service.update_progress(video.task_id, 50)

        # 生成字幕
        subtitle_service = SubtitleService(db)
        subtitle = subtitle_service.generate_automatic_subtitle(video_id, audio_track_id)

        # 导出SRT文件
        srt_output_dir = f"uploads/subtitles/video_{video_id}"
        os.makedirs(srt_output_dir, exist_ok=True)
        srt_file_path = os.path.join(srt_output_dir, f"auto_subtitle_{subtitle.id}.srt")

        subtitle_service.export_subtitle_to_srt(subtitle.id, srt_file_path)

        # 更新字幕文件路径
        subtitle.file_path = srt_file_path
        db.commit()

        # 完成任务
        current_task.update_state(
            state='PROGRESS',
            meta={'current': 2, 'total': 2, 'status': '字幕生成完成'}
        )
        task_service.update_progress(video.task_id, 100)
        task_service.set_status(video.task_id, "completed")

        return {
            'status': 'completed',
            'subtitle_id': subtitle.id,
            'confidence': subtitle.confidence,
            'srt_file': srt_file_path,
            'video_id': video_id
        }

    except Exception as e:
        # 错误处理
        task_service = TaskService(db)
        task_service.set_status(video.task_id, "failed")

        current_task.update_state(
            state='FAILURE',
            meta={'error': str(e)}
        )
        raise e
    finally:
        db.close()


@celery.task(bind=True)
def analyze_video_content(self, video_id: int):
    """分析视频内容要素"""
    db = SessionLocal()
    try:
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise Exception(f"Video {video_id} not found")
        
        # 模拟内容分析
        for i in range(8):
            time.sleep(1)
            progress = 20 + (i + 1) * 10  # 从20%开始到100%
            
            task_service = TaskService(db)
            task_service.update_progress(video.task_id, progress)
            
            current_task.update_state(
                state='PROGRESS',
                meta={'current': i + 1, 'total': 8, 'status': f'分析内容要素... {progress}%'}
            )
        
        # 模拟分析结果
        result_data = {
            "characters": [
                {
                    "id": 1,
                    "name": "角色A",
                    "appearances": [
                        {"start": 0, "end": 30, "confidence": 0.95},
                        {"start": 60, "end": 90, "confidence": 0.88}
                    ],
                    "emotions": ["happy", "sad", "angry"],
                    "clothing": "modern_casual"
                }
            ],
            "scenes": [
                {
                    "type": "indoor",
                    "style": "modern",
                    "location": "living_room",
                    "start": 0,
                    "end": 45,
                    "confidence": 0.92
                }
            ],
            "objects": [
                {"name": "phone", "confidence": 0.85, "times": [15, 67]},
                {"name": "car", "confidence": 0.78, "times": [89]}
            ]
        }
        
        # 保存分析结果
        analysis_result = AnalysisResult(
            video_id=video_id,
            step="content_analysis",
            result=result_data,
            confidence=0.88,
            processing_time=8.0
        )
        
        db.add(analysis_result)
        db.commit()
        
        return {
            'status': 'completed',
            'result': result_data,
            'video_id': video_id
        }
        
    except Exception as e:
        current_task.update_state(
            state='FAILURE',
            meta={'error': str(e)}
        )
        raise e
    finally:
        db.close()


@celery.task(bind=True)
def analyze_video_plot(self, video_id: int):
    """分析视频剧情"""
    db = SessionLocal()
    try:
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise Exception(f"Video {video_id} not found")
        
        # 模拟剧情分析
        for i in range(5):
            time.sleep(2)
            progress = 80 + (i + 1) * 4  # 从80%到100%
            
            task_service = TaskService(db)
            task_service.update_progress(video.task_id, progress)
            
            current_task.update_state(
                state='PROGRESS',
                meta={'current': i + 1, 'total': 5, 'status': f'分析剧情结构... {progress}%'}
            )
        
        # 模拟分析结果
        result_data = {
            "dialogues": [
                {
                    "start": 5.2,
                    "end": 8.7,
                    "speaker": "角色A",
                    "text": "你好，今天天气真不错",
                    "emotion": "happy",
                    "confidence": 0.92
                }
            ],
            "plot_points": [
                {
                    "type": "introduction",
                    "start": 0,
                    "end": 20,
                    "importance": 0.8
                },
                {
                    "type": "conflict",
                    "start": 45,
                    "end": 75,
                    "importance": 0.95
                }
            ],
            "emotion_curve": [
                {"time": 0, "emotion": "neutral", "intensity": 0.5},
                {"time": 30, "emotion": "happy", "intensity": 0.8},
                {"time": 60, "emotion": "sad", "intensity": 0.3}
            ]
        }
        
        # 保存分析结果
        analysis_result = AnalysisResult(
            video_id=video_id,
            step="plot_analysis",
            result=result_data,
            confidence=0.85,
            processing_time=10.0
        )
        
        db.add(analysis_result)
        
        # 完成任务
        task_service = TaskService(db)
        task_service.update_progress(video.task_id, 100)
        task_service.set_status(video.task_id, "completed")
        
        db.commit()
        
        return {
            'status': 'completed',
            'result': result_data,
            'video_id': video_id
        }
        
    except Exception as e:
        current_task.update_state(
            state='FAILURE',
            meta={'error': str(e)}
        )
        raise e
    finally:
        db.close()


@celery.task(bind=True)
def process_task_videos(self, task_id: int):
    """处理任务中的所有视频 - 任务编排主入口"""
    logger.info(f"Processing task {task_id}")
    db = SessionLocal()
    try:
        # 获取任务信息
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise Exception(f"Task {task_id} not found")

        # 获取任务中的所有视频
        videos = db.query(Video).filter(Video.task_id == task_id).all()
        if not videos:
            raise Exception(f"No videos found for task {task_id}")

        # 更新任务状态
        task_service = TaskService(db)
        task_service.set_status(task_id, "processing")
        task_service.update_progress(task_id, 0.0)

        current_task.update_state(
            state='PROGRESS',
            meta={
                'current': 0,
                'total': len(videos),
                'status': f'开始处理任务中的 {len(videos)} 个视频...'
            }
        )

        # 为每个视频创建分析任务链
        video_tasks = []
        for i, video in enumerate(videos):
            # 更新视频状态
            video.status = "analyzing"

            # 创建视频分析任务链：基础信息 -> 内容分析 -> 剧情分析
            video_chain = chain(
                analyze_video_basic_info.s(video.id),
                analyze_video_content.si(video.id),
                analyze_video_plot.si(video.id)
            )
            video_tasks.append(video_chain)

        db.commit()

        # 并行执行所有视频的分析任务
        job = group(video_tasks)
        result = job.apply_async()

        # 等待所有任务完成
        completed_count = 0
        total_videos = len(videos)

        while not result.ready():
            time.sleep(2)

            # 检查已完成的视频数量
            completed_videos = db.query(Video).filter(
                Video.task_id == task_id,
                Video.status == "analyzed"
            ).count()

            if completed_videos > completed_count:
                completed_count = completed_videos
                progress = (completed_count / total_videos) * 100

                task_service.update_progress(task_id, progress)
                current_task.update_state(
                    state='PROGRESS',
                    meta={
                        'current': completed_count,
                        'total': total_videos,
                        'status': f'已完成 {completed_count}/{total_videos} 个视频的分析'
                    }
                )

        # 检查是否所有任务都成功完成
        failed_videos = db.query(Video).filter(
            Video.task_id == task_id,
            Video.status == "failed"
        ).count()

        if failed_videos > 0:
            # 有失败的视频
            task_service.set_status(task_id, "failed")
            task_service.update_progress(task_id, 100.0)

            current_task.update_state(
                state='FAILURE',
                meta={'error': f'{failed_videos} 个视频分析失败'}
            )
            raise Exception(f'{failed_videos} 个视频分析失败')
        else:
            # 所有视频都成功完成
            task_service.set_status(task_id, "completed")
            task_service.update_progress(task_id, 100.0)

            return {
                'status': 'completed',
                'task_id': task_id,
                'videos_processed': total_videos,
                'message': f'成功完成 {total_videos} 个视频的分析'
            }

    except Exception as e:
        # 错误处理
        task_service = TaskService(db)
        task_service.set_status(task_id, "failed")

        current_task.update_state(
            state='FAILURE',
            meta={'error': str(e)}
        )
        raise e
    finally:
        db.close()


@celery.task(bind=True)
def process_single_video_complete_analysis(self, video_id: int):
    """处理单个视频的完整分析流程"""
    db = SessionLocal()
    try:
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise Exception(f"Video {video_id} not found")

        # 更新视频状态
        video.status = "analyzing"
        db.commit()

        current_task.update_state(
            state='PROGRESS',
            meta={'current': 0, 'total': 3, 'status': '开始视频分析...'}
        )

        # 创建分析任务链
        analysis_chain = chain(
            analyze_video_basic_info.s(video_id),
            analyze_video_content.si(video_id),
            analyze_video_plot.si(video_id)
        )

        # 执行分析链
        result = analysis_chain.apply_async()

        # 等待完成
        while not result.ready():
            time.sleep(1)

        # 检查结果
        if result.successful():
            return {
                'status': 'completed',
                'video_id': video_id,
                'message': '视频分析完成'
            }
        else:
            raise Exception('视频分析失败')

    except Exception as e:
        # 更新视频状态为失败
        video = db.query(Video).filter(Video.id == video_id).first()
        if video:
            video.status = "failed"
            db.commit()

        current_task.update_state(
            state='FAILURE',
            meta={'error': str(e)}
        )
        raise e
    finally:
        db.close()
